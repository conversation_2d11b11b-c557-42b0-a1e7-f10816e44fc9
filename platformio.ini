[platformio]
default_envs = lilygo-t-display-s3

[env]
platform = espressif32
framework = arduino
monitor_speed = 115200
board_build.filesystem = littlefs

[env:lilygo-t-display-s3]
board = lilygo-t-display-s3
build_flags = 
	-D LILYGO_T_DISPLAY_S3
lib_deps = 
	lovyan03/LovyanGFX@^1.2.7
	olikraus/U8g2@^2.36.5
	hafidh/Callmebot ESP32@^2.0.0
	bblanchon/<PERSON><PERSON><PERSON>o<PERSON><PERSON>@^7.3.1
	yawom/ESP32ButtonHandler@^2.0.0